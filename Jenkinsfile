pipeline {
  agent none

  options {
    timeout(time: 60, unit: 'MINUTES')
    retry(2)
    buildDiscarder(logRotator(numToKeepStr: '10'))
    skipDefaultCheckout()
  }

  environment {
    HARBOR_REGISTRY = 'harbor.zhixin.asia'
    FRONTEND_IMAGE = "${HARBOR_REGISTRY}/ops-platform/frontend"
    BACKEND_IMAGE = "${HARBOR_REGISTRY}/ops-platform/backend"
    NODE_ENV = 'production'
    PYTHON_ENV = 'production'
    // npm镜像源配置
    NPM_REGISTRY = 'https://registry.npmmirror.com'
    NPM_REGISTRY_FALLBACK = 'https://registry.npmjs.org'
    // 构建资源配置
    NODE_OPTIONS = '--max-old-space-size=2048'
  }
  
  stages {
    stage('Checkout Code') {
      agent any
      steps {
        checkout scm
        script {
          env.GIT_COMMIT_SHORT = sh(
            script: 'git rev-parse --short HEAD',
            returnStdout: true
          ).trim()
          env.BUILD_TAG = "ops-platform-${env.BUILD_NUMBER}-${env.GIT_COMMIT_SHORT}"
          env.BUILD_TIMESTAMP = sh(
            script: 'date +%Y%m%d-%H%M%S',
            returnStdout: true
          ).trim()
        }
        echo "Git commit: ${env.GIT_COMMIT_SHORT}"
        echo "Build tag: ${env.BUILD_TAG}"
        echo "Build timestamp: ${env.BUILD_TIMESTAMP}"

        // 保存代码到stash，供后续stage使用
        stash includes: '**', name: 'source-code'
      }
    }

    // 并行构建前端和后端
    stage('Parallel Build') {
      parallel {
        stage('Frontend Build') {
          agent {
            kubernetes {
              yaml '''
apiVersion: v1
kind: Pod
spec:
  securityContext:
    runAsUser: 1000
    runAsNonRoot: true
    fsGroup: 1000
  containers:
  - name: node
    image: node:22-alpine
    command: ['cat']
    tty: true
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      runAsNonRoot: true
    resources:
      requests:
        memory: "1Gi"
        cpu: "500m"
      limits:
        memory: "3Gi"
        cpu: "1000m"
    env:
    - name: NODE_OPTIONS
      value: "--max-old-space-size=2048"
'''
            }
          }
          steps {
            container('node') {
              // 恢复代码
              unstash 'source-code'

              script {
                try {
                  sh '''
                    echo "开始前端构建..."
                    cd frontend

                    # 显示环境信息
                    echo "Node.js版本: $(node --version)"
                    echo "npm版本: $(npm --version)"
                    echo "当前工作目录: $(pwd)"
                    echo "可用内存: $(free -h)"

                    # 智能配置npm镜像源
                    echo "配置npm镜像源..."
                    configure_npm_registry() {
                      echo "尝试配置npm镜像源: $1"
                      if npm config set registry "$1" && npm ping --registry "$1" > /dev/null 2>&1; then
                        echo "成功配置npm镜像源: $1"
                        return 0
                      else
                        echo "镜像源不可用: $1"
                        return 1
                      fi
                    }

                    # 尝试使用国内镜像源，失败则回退到官方源
                    if ! configure_npm_registry "${NPM_REGISTRY}"; then
                      echo "国内镜像源不可用，回退到官方源"
                      configure_npm_registry "${NPM_REGISTRY_FALLBACK}"
                    fi

                    # 显示当前配置
                    echo "当前npm配置:"
                    npm config get registry

                    # 清理并安装依赖
                    echo "清理旧的依赖..."
                    rm -rf node_modules package-lock.json

                    echo "安装依赖..."
                    npm ci --prefer-offline --no-audit --progress=false

                    # 验证关键依赖
                    echo "验证TypeScript和Vue相关依赖..."
                    npm list typescript vue-tsc @types/node || true

                    # 类型检查
                    echo "执行TypeScript类型检查..."
                    npx vue-tsc --noEmit || {
                      echo "类型检查失败，但继续构建过程"
                    }

                    # 构建应用
                    echo "开始构建生产版本..."
                    npm run build

                    # 验证构建结果
                    if [ ! -d "dist" ]; then
                      echo "构建失败：dist目录不存在"
                      exit 1
                    fi

                    echo "前端构建完成，产物大小："
                    du -sh dist/
                  '''
                } catch (Exception e) {
                  echo "前端构建失败: ${e.getMessage()}"
                  throw e
                }
              }
            }
          }
    }

        stage('Backend Build & Test') {
          agent {
            kubernetes {
              yaml '''
apiVersion: v1
kind: Pod
spec:
  securityContext:
    runAsUser: 1000
    runAsNonRoot: true
    fsGroup: 1000
  containers:
  - name: python
    image: python:3.11-slim
    command: ['cat']
    tty: true
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      runAsNonRoot: true
    resources:
      requests:
        memory: "1Gi"
        cpu: "500m"
      limits:
        memory: "2Gi"
        cpu: "1000m"
    env:
    - name: PYTHONUNBUFFERED
      value: "1"
    - name: PIP_INDEX_URL
      value: "https://pypi.tuna.tsinghua.edu.cn/simple"
    - name: PIP_TRUSTED_HOST
      value: "pypi.tuna.tsinghua.edu.cn"
'''
            }
          }
          steps {
            container('python') {
              // 恢复代码
              unstash 'source-code'

              script {
                try {
                  sh '''
                    echo "开始后端构建和测试..."
                    cd backend

                    # 显示环境信息
                    echo "Python版本: $(python --version)"
                    echo "pip版本: $(pip --version)"
                    echo "当前工作目录: $(pwd)"
                    echo "可用内存: $(free -h)"

                    # 安装系统依赖
                    echo "安装系统依赖..."
                    apt-get update -qq && apt-get install -y -qq gcc g++ libpq-dev libffi-dev

                    # 安装uv包管理器
                    echo "安装uv包管理器..."
                    pip install --no-cache-dir uv

                    # 安装项目依赖
                    echo "安装项目依赖..."
                    uv sync --no-dev || {
                      echo "uv sync失败，尝试使用pip安装..."
                      pip install -r requirements.txt
                    }

                    # 代码质量检查
                    echo "执行代码质量检查..."
                    if command -v black >/dev/null 2>&1; then
                      uv run black --check --diff . || echo "Black检查失败，但继续构建"
                    fi

                    if command -v isort >/dev/null 2>&1; then
                      uv run isort --check-only --diff . || echo "isort检查失败，但继续构建"
                    fi

                    if command -v flake8 >/dev/null 2>&1; then
                      uv run flake8 . || echo "flake8检查失败，但继续构建"
                    fi

                    # 运行测试
                    echo "开始运行测试..."
                    if [ -d "tests" ] && [ "$(ls -A tests)" ]; then
                      uv run pytest tests/ --maxfail=1 --disable-warnings --tb=short || {
                        echo "测试失败，但继续构建过程"
                      }
                    else
                      echo "未找到测试文件，跳过测试"
                    fi

                    echo "后端构建和测试完成"
                  '''
                } catch (Exception e) {
                  echo "后端构建失败: ${e.getMessage()}"
                  throw e
                }
              }
            }
          }
        }
      }
    }

    stage('Build & Push Docker Images') {
      agent {
        kubernetes {
          yaml '''
apiVersion: v1
kind: Pod
spec:
  securityContext:
    runAsUser: 1000
    runAsNonRoot: true
    fsGroup: 1000
  containers:
  - name: kaniko
    image: gcr.io/kaniko-project/executor:v1.23.2-debug
    command: ["/busybox/cat"]
    tty: true
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      runAsNonRoot: true
    resources:
      requests:
        memory: "2Gi"
        cpu: "1000m"
      limits:
        memory: "4Gi"
        cpu: "2000m"
    volumeMounts:
    - name: kaniko-secret
      mountPath: /kaniko/.docker
      readOnly: true
  volumes:
  - name: kaniko-secret
    secret:
      secretName: harbor-registry-secret
      items:
      - key: .dockerconfigjson
        path: config.json
'''
        }
      }
      steps {
        container('kaniko') {
          // 恢复代码
          unstash 'source-code'

          script {
            try {
              sh '''
                echo "开始使用Kaniko构建和推送Docker镜像..."

                # 验证Kaniko配置
                if [ ! -f /kaniko/.docker/config.json ]; then
                  echo "错误: Harbor认证配置不存在"
                  exit 1
                fi

                echo "构建前端镜像..."
                /kaniko/executor \\
                  --context=./frontend \\
                  --dockerfile=./frontend/Dockerfile \\
                  --destination=$FRONTEND_IMAGE:$BUILD_TAG \\
                  --destination=$FRONTEND_IMAGE:latest \\
                  --cache=true \\
                  --cache-ttl=24h \\
                  --compressed-caching=false \\
                  --snapshot-mode=redo \\
                  --use-new-run \\
                  --log-timestamp

                echo "构建后端镜像..."
                /kaniko/executor \\
                  --context=./backend \\
                  --dockerfile=./backend/Dockerfile \\
                  --destination=$BACKEND_IMAGE:$BUILD_TAG \\
                  --destination=$BACKEND_IMAGE:latest \\
                  --cache=true \\
                  --cache-ttl=24h \\
                  --compressed-caching=false \\
                  --snapshot-mode=redo \\
                  --use-new-run \\
                  --log-timestamp

                echo "Docker镜像构建和推送完成"
                echo "前端镜像: $FRONTEND_IMAGE:$BUILD_TAG"
                echo "后端镜像: $BACKEND_IMAGE:$BUILD_TAG"
              '''
            } catch (Exception e) {
              echo "Docker镜像构建失败: ${e.getMessage()}"
              throw e
            }
          }
        }
      }
    }

  }
  
  post {
    always {
      script {
        echo "CI 流程执行完毕"
        echo "构建标签: ${env.BUILD_TAG}"
        echo "构建时间戳: ${env.BUILD_TIMESTAMP}"
        echo "Git提交: ${env.GIT_COMMIT_SHORT}"

        // 发布构建报告
        publishHTML([
          allowMissing: false,
          alwaysLinkToLastBuild: true,
          keepAll: true,
          reportDir: '.',
          reportFiles: 'build-report.html',
          reportName: 'Build Report',
          reportTitles: ''
        ])
      }
    }

    success {
      script {
        echo "🎉 构建成功！"
        echo "前端镜像: ${FRONTEND_IMAGE}:${env.BUILD_TAG}"
        echo "后端镜像: ${BACKEND_IMAGE}:${env.BUILD_TAG}"
        echo "请检查 Harbor 镜像仓库获取最新镜像"

        // 成功通知（可配置钉钉、企业微信等）
        // dingtalk(
        //   robot: 'jenkins-bot',
        //   type: 'MARKDOWN',
        //   title: 'OPS-Platform 构建成功',
        //   text: [
        //     "## 🎉 构建成功",
        //     "**项目**: OPS-Platform",
        //     "**分支**: ${env.BRANCH_NAME}",
        //     "**构建号**: ${env.BUILD_NUMBER}",
        //     "**提交**: ${env.GIT_COMMIT_SHORT}",
        //     "**镜像**: ${FRONTEND_IMAGE}:${env.BUILD_TAG}"
        //   ]
        // )
      }
    }

    failure {
      script {
        echo "❌ 构建失败，请查看 Jenkins 控制台日志"
        echo "构建时间: ${env.BUILD_TIMESTAMP}"
        echo "失败阶段: ${env.STAGE_NAME}"

        // 失败通知
        // dingtalk(
        //   robot: 'jenkins-bot',
        //   type: 'MARKDOWN',
        //   title: 'OPS-Platform 构建失败',
        //   text: [
        //     "## ❌ 构建失败",
        //     "**项目**: OPS-Platform",
        //     "**分支**: ${env.BRANCH_NAME}",
        //     "**构建号**: ${env.BUILD_NUMBER}",
        //     "**失败阶段**: ${env.STAGE_NAME}",
        //     "**日志**: [查看详情](${env.BUILD_URL}console)"
        //   ]
        // )
      }
    }

    cleanup {
      script {
        echo "清理工作空间..."
        // 清理临时文件
        sh '''
          find . -name "*.tmp" -delete 2>/dev/null || true
          find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
          find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
        '''
      }
    }
  }
}
