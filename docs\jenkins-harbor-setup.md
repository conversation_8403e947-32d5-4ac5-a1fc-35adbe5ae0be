# Jenkins Harbor 认证配置指南

## 概述

本文档说明如何为Jenkins Kubernetes插件配置Harbor镜像仓库认证，以支持使用Kaniko进行安全的容器镜像构建。

## 前置条件

1. Kubernetes集群已部署
2. Jenkins已安装Kubernetes插件
3. Harbor镜像仓库已部署并可访问
4. 具有Harbor管理员权限

## 配置步骤

### 1. 创建Harbor机器人账户

```bash
# 在Harbor Web界面中创建机器人账户
# 项目 -> 机器人账户 -> 新建机器人账户
# 名称: jenkins-kaniko
# 权限: Push, Pull
```

### 2. 创建Docker配置文件

```bash
# 创建Docker认证配置
kubectl create secret docker-registry harbor-registry-secret \
  --docker-server=harbor.zhixin.asia \
  --docker-username=robot$jenkins-kaniko \
  --docker-password=<ROBOT_TOKEN> \
  --namespace=jenkins
```

### 3. 验证Secret创建

```bash
# 验证Secret是否正确创建
kubectl get secret harbor-registry-secret -n jenkins -o yaml

# 检查配置内容
kubectl get secret harbor-registry-secret -n jenkins -o jsonpath='{.data.\.dockerconfigjson}' | base64 -d
```

### 4. 配置Jenkins凭据

在Jenkins中配置Harbor凭据（备用方案）：

1. 进入 Jenkins -> 凭据 -> 系统 -> 全局凭据
2. 添加凭据类型：用户名和密码
3. ID: `dev-harbor`
4. 用户名: `robot$jenkins-kaniko`
5. 密码: `<ROBOT_TOKEN>`

## 安全最佳实践

### 1. 最小权限原则

- 机器人账户只授予必要的Push/Pull权限
- 限制机器人账户的项目访问范围
- 定期轮换机器人账户Token

### 2. 网络安全

```yaml
# 网络策略示例 - 限制Jenkins Pod访问
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: jenkins-harbor-access
  namespace: jenkins
spec:
  podSelector:
    matchLabels:
      jenkins: kaniko
  policyTypes:
  - Egress
  egress:
  - to:
    - namespaceSelector: {}
    ports:
    - protocol: TCP
      port: 443  # Harbor HTTPS
```

### 3. Secret管理

```bash
# 使用Sealed Secrets或External Secrets管理敏感信息
# 示例：使用External Secrets
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: harbor-secret-store
  namespace: jenkins
spec:
  provider:
    vault:
      server: "https://vault.example.com"
      path: "secret"
      version: "v2"
      auth:
        kubernetes:
          mountPath: "kubernetes"
          role: "jenkins"
```

## 故障排除

### 1. 认证失败

```bash
# 检查Secret格式
kubectl get secret harbor-registry-secret -n jenkins -o jsonpath='{.data.\.dockerconfigjson}' | base64 -d | jq .

# 测试Harbor连接
curl -u "robot\$jenkins-kaniko:<TOKEN>" https://harbor.zhixin.asia/v2/_catalog
```

### 2. Kaniko构建失败

```bash
# 检查Kaniko Pod日志
kubectl logs -f <kaniko-pod-name> -n jenkins

# 验证挂载的配置
kubectl exec -it <kaniko-pod-name> -n jenkins -- cat /kaniko/.docker/config.json
```

### 3. 网络连接问题

```bash
# 测试从Jenkins Pod到Harbor的连接
kubectl run test-pod --image=curlimages/curl -it --rm -- \
  curl -v https://harbor.zhixin.asia/v2/

# 检查DNS解析
kubectl run test-pod --image=busybox -it --rm -- \
  nslookup harbor.zhixin.asia
```

## 监控和日志

### 1. 构建监控

```yaml
# Prometheus监控规则示例
groups:
- name: jenkins-kaniko
  rules:
  - alert: KanikoBuildFailure
    expr: increase(jenkins_builds_failed_total{job="kaniko"}[5m]) > 0
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: "Kaniko构建失败"
      description: "Jenkins Kaniko构建在过去5分钟内失败"
```

### 2. 日志聚合

```yaml
# Fluentd配置示例
<source>
  @type kubernetes_metadata
  @id input_kubernetes_metadata
</source>

<filter kubernetes.**>
  @type grep
  <regexp>
    key $.kubernetes.labels.jenkins
    pattern ^kaniko$
  </regexp>
</filter>
```

## 参考资源

- [Kaniko官方文档](https://github.com/GoogleContainerTools/kaniko)
- [Harbor文档](https://goharbor.io/docs/)
- [Jenkins Kubernetes插件](https://plugins.jenkins.io/kubernetes/)
- [Kubernetes Secrets](https://kubernetes.io/docs/concepts/configuration/secret/)
