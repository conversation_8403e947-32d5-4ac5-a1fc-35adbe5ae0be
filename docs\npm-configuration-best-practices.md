# npm配置最佳实践指南

## 概述

本文档基于Context7工具分析的npm官方最佳实践，提供了在CI/CD环境中安全、高效配置npm的指导原则。

## 🚨 原配置问题分析

### 发现的问题

```bash
# ❌ 原配置存在的问题
npm config set registry https://registry.npmmirror.com
npm config set disturl https://npmmirror.com/dist
npm config set electron_mirror https://npmmirror.com/mirrors/electron/
npm config set sass_binary_site https://npmmirror.com/mirrors/node-sass/
npm config set phantomjs_cdnurl https://npmmirror.com/mirrors/phantomjs/
npm config set chromedriver_cdnurl https://npmmirror.com/mirrors/chromedriver/
npm config set operadriver_cdnurl https://npmmirror.com/mirrors/operadriver/
npm config set fse_binary_host_mirror https://npmmirror.com/mirrors/fsevents/
```

**问题分析：**
1. **过度配置**: 配置了很多不必要的镜像源
2. **安全风险**: 没有验证镜像源的可用性和安全性
3. **维护性差**: 硬编码了镜像源地址
4. **缺少备用方案**: 如果镜像源不可用，没有回退机制
5. **SSL验证**: 没有考虑SSL证书验证问题

## ✅ 改进后的配置

### 1. 智能镜像源配置

```bash
# ✅ 改进的配置方案
configure_npm_registry() {
  echo "尝试配置npm镜像源: $1"
  if npm config set registry "$1" && npm ping --registry "$1" > /dev/null 2>&1; then
    echo "成功配置npm镜像源: $1"
    return 0
  else
    echo "镜像源不可用: $1"
    return 1
  fi
}

# 尝试使用国内镜像源，失败则回退到官方源
if ! configure_npm_registry "${NPM_REGISTRY}"; then
  echo "国内镜像源不可用，回退到官方源"
  configure_npm_registry "${NPM_REGISTRY_FALLBACK}"
fi
```

### 2. 环境变量配置

```bash
# 在Jenkinsfile中使用环境变量
environment {
  NPM_REGISTRY = 'https://registry.npmmirror.com'
  NPM_REGISTRY_FALLBACK = 'https://registry.npmjs.org'
  NODE_OPTIONS = '--max-old-space-size=2048'
}
```

## 🔒 安全最佳实践

### 1. SSL证书验证

```bash
# ✅ 保持SSL验证开启（默认）
npm config get strict-ssl  # 应该返回 true

# ❌ 不要禁用SSL验证
# npm config set strict-ssl false  # 危险操作
```

### 2. 认证配置

```bash
# ✅ 正确的认证配置（作用域限定）
//registry.npmjs.org/:_authToken=${NPM_TOKEN}
//registry.npmmirror.com/:_authToken=${NPM_TOKEN}

# ❌ 错误的认证配置（全局）
# _authToken=MYTOKEN  # 会泄露到所有registry
```

### 3. 代理配置

```bash
# ✅ 正确的代理配置
npm config set proxy http://proxy.company.com:8080
npm config set https-proxy http://proxy.company.com:8080
npm config set noproxy "localhost,127.0.0.1,*.company.com"

# 或使用环境变量
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080
export NO_PROXY="localhost,127.0.0.1,*.company.com"
```

## 🚀 性能优化

### 1. 缓存配置

```bash
# 启用离线模式（CI环境推荐）
npm ci --prefer-offline --no-audit --progress=false

# 配置缓存目录
npm config set cache /tmp/npm-cache
npm config set cache-min 86400  # 24小时
```

### 2. 并发配置

```bash
# 限制并发下载数（避免网络拥塞）
npm config set maxsockets 5
npm config set network-concurrency 5
```

### 3. 超时配置

```bash
# 设置合理的超时时间
npm config set timeout 60000  # 60秒
npm config set fetch-timeout 60000
```

## 📋 CI/CD环境配置

### 1. .npmrc文件配置

```ini
# 项目根目录的.npmrc文件
registry=https://registry.npmmirror.com/
strict-ssl=true
audit=false
fund=false
progress=false
loglevel=warn

# 作用域配置
@company:registry=https://npm.company.com/
//npm.company.com/:_authToken=${NPM_TOKEN}
```

### 2. Docker环境配置

```dockerfile
# Dockerfile中的npm配置
FROM node:22-alpine

# 设置npm配置
RUN npm config set registry https://registry.npmmirror.com && \
    npm config set audit false && \
    npm config set fund false && \
    npm config set progress false

# 复制配置文件
COPY .npmrc /root/.npmrc

# 安装依赖
COPY package*.json ./
RUN npm ci --only=production
```

### 3. Kubernetes环境配置

```yaml
# ConfigMap for npm configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: npm-config
data:
  .npmrc: |
    registry=https://registry.npmmirror.com/
    strict-ssl=true
    audit=false
    fund=false
    progress=false
    loglevel=warn
```

## 🔧 故障排除

### 1. 网络连接问题

```bash
# 测试registry连接
npm ping --registry https://registry.npmmirror.com
npm ping --registry https://registry.npmjs.org

# 检查网络配置
npm config get registry
npm config get proxy
npm config get https-proxy
```

### 2. 认证问题

```bash
# 检查认证配置
npm config get //registry.npmjs.org/:_authToken
npm whoami --registry https://registry.npmjs.org

# 修复认证配置
npm config fix
```

### 3. 缓存问题

```bash
# 清理npm缓存
npm cache clean --force

# 验证缓存
npm cache verify
```

## 📊 监控和日志

### 1. 构建时间监控

```bash
# 记录安装时间
time npm ci --prefer-offline --no-audit

# 分析依赖大小
npm ls --depth=0 --json | jq '.dependencies | keys | length'
```

### 2. 安全审计

```bash
# 定期运行安全审计
npm audit --audit-level moderate

# 自动修复安全问题
npm audit fix
```

## 🎯 推荐配置模板

### 生产环境配置

```bash
# 生产环境推荐配置
npm config set registry https://registry.npmmirror.com
npm config set strict-ssl true
npm config set audit false
npm config set fund false
npm config set progress false
npm config set loglevel warn
npm config set cache /tmp/npm-cache
npm config set timeout 60000
```

### 开发环境配置

```bash
# 开发环境推荐配置
npm config set registry https://registry.npmmirror.com
npm config set strict-ssl true
npm config set audit true
npm config set fund true
npm config set progress true
npm config set loglevel info
```

## 📚 参考资源

- [npm官方配置文档](https://docs.npmjs.com/cli/v8/using-npm/config)
- [npm镜像源列表](https://npmmirror.com/)
- [npm安全最佳实践](https://docs.npmjs.com/auditing-package-dependencies-for-security-vulnerabilities)
- [CI/CD中的npm配置](https://docs.npmjs.com/using-private-packages-in-a-ci-cd-workflow)
